import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

const SearchableSelect = React.forwardRef(({ 
    options = [], 
    value, 
    onValueChange, 
    placeholder = "Select an option...",
    emptyMessage = "No options found",
    className,
    ...props 
}, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const containerRef = useRef(null);
    const inputRef = useRef(null);

    // Filter options based on search term
    const filteredOptions = options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsOpen(false);
                setSearchTerm('');
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Focus input when dropdown opens
    useEffect(() => {
        if (isOpen && inputRef.current) {
            inputRef.current.focus();
        }
    }, [isOpen]);

    const handleSelect = (optionValue) => {
        onValueChange(optionValue);
        setIsOpen(false);
        setSearchTerm('');
    };

    const getSelectedLabel = () => {
        const selectedOption = options.find(option => option.value === value);
        return selectedOption ? selectedOption.label : placeholder;
    };

    return (
        <div ref={containerRef} className={cn("relative", className)} {...props}>
            <Button
                type="button"
                variant="outline"
                role="combobox"
                aria-expanded={isOpen}
                className="w-full justify-between"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className="truncate">{getSelectedLabel()}</span>
                <span className="ml-2">▼</span>
            </Button>

            {isOpen && (
                <div className="absolute top-full left-0 w-full mt-1 z-50 bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-2">
                        <Input
                            ref={inputRef}
                            placeholder="Search..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full"
                        />
                    </div>
                    <ScrollArea className="h-60">
                        <div className="py-1">
                            {filteredOptions.length > 0 ? (
                                filteredOptions.map((option) => (
                                    <Button
                                        key={option.value}
                                        variant="ghost"
                                        className="w-full justify-start rounded-none"
                                        onClick={() => handleSelect(option.value)}
                                    >
                                        {option.label}
                                    </Button>
                                ))
                            ) : (
                                <div className="py-2 text-center text-gray-500">
                                    {emptyMessage}
                                </div>
                            )}
                        </div>
                    </ScrollArea>
                </div>
            )}
        </div>
    );
});

SearchableSelect.displayName = "SearchableSelect";

export { SearchableSelect };
