import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';

const PositionsTitlesCard = ({ positionTitles, session, getCustomerUsersInfo }) => {
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [newPositionTitle, setNewPositionTitle] = useState('');
    const { post, get, put } = useApiClient();
    const { toast } = useToast();
    const [positionTitlesData, setPositionTitlesData] = useState([]);
    useEffect(() => {
        if (positionTitles) {
            setPositionTitlesData(positionTitles);
        }
    }, [positionTitles]);
    const handleAddPositionTitle = async () => {
        console.log('handleAddPositionTitle', newPositionTitle);
        try {
            const response = await put('/api/tenant/position_titles', { position_title: newPositionTitle });
            if (response && response.data) {
                toast({
                    title: "Success",
                    description: "Position Title created successfully",
                });
                setIsAddModalOpen(false);
                setNewPositionTitle('');
                // Refresh position titles data
                getCustomerUsersInfo();
            }
        } catch (error) {
            console.error('Error creating position title:', error);
            toast({
                title: "Error",
                description: "Failed to create position title",
                variant: "destructive",
            });
        }
    };
    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">

                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium">Position Titles</h2>
                    <Button type="button" onClick={() => setIsAddModalOpen(true)}>
                        +
                    </Button>
                </div>
                <div className="mt-6">
                    <ul className="list-disc list-inside">
                        {positionTitlesData.map((title) => (
                            <li key={title._id}>{title.position_title}</li>
                        ))}
                    </ul>
                </div>

                {/* Add Position Title Dialog */}
                <div>
                        <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                            <DialogContent className="max-w-md">
                                <DialogHeader>
                                    <DialogTitle>Add New Position Title</DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                    <div>
                                        <Label className="text-sm font-medium">Position Title</Label>
                                        <Input
                                            name="position_title"
                                            value={newPositionTitle}
                                            onChange={(e) => setNewPositionTitle(e.target.value)}
                                            placeholder="Enter position title"
                                            className="mt-1"
                                        />
                                    </div>
                                </div>
                                <DialogFooter>
                                    <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                                        Cancel
                                    </Button>
                                    <Button 
                                        type="button"
                                        onClick={handleAddPositionTitle}
                                        disabled={!newPositionTitle}
                                    >
                                        Add Title
                                    </Button>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                </div>
                
        </div>
    )
}


export default PositionsTitlesCard;
