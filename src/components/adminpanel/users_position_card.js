import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { SearchableSelect } from '@/components/ui/searchable-select';
import {
    PencilIcon
} from '@heroicons/react/24/outline';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { useToast } from "@/lib/hooks/use-toast"

const PositionzCard = ({ positionz, session, getCustomerUsersInfo }) => {
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedPosition, setSelectedPosition] = useState(null);
    const [positionTitles, setPositionTitles] = useState([]);
    const [positions, setPositions] = useState([]);
    const [newPosition, setNewPosition] = useState({
        position_title_id: '',
        level: '',
        grade: '',
        parent_position_id: ''
    });
    const { post, get, put } = useApiClient();
    const { toast } = useToast();

    useEffect(() => {
        if (positionz) {
            setPositions(positionz);
            console.log('positionsx:', positionz);
        }
    }, [positionz]);
    // Fetch position titles and positions when component mounts
    // useEffect(() => {
    //     const fetchData = async () => {
    //         try {
    //             // Fetch position titles
    //             const titlesResponse = await post('/api/tenant/position_titles');
    //             if (titlesResponse && titlesResponse.data) {
    //                 setPositionTitles(titlesResponse.data);
    //             }
                
    //             // Fetch positions
    //             const positionsResponse = await post('/api/tenant/positions');
    //             if (positionsResponse && positionsResponse.data) {
    //                 setPositions(positionsResponse.data);
    //             }
    //         } catch (error) {
    //             console.error('Error fetching data:', error);
    //             toast({
    //                 title: "Error",
    //                 description: "Failed to fetch data",
    //                 variant: "destructive",
    //             });
    //         }
    //     };

    //     fetchData();
    // }, []);

    const handleAddPosition = async () => {
        try {
            const response = await post('/api/tenant/positions', newPosition);
            if (response && response.data) {
                toast({
                    title: "Success",
                    description: "Position created successfully",
                });
                setIsAddModalOpen(false);
                setNewPosition({
                    position_title_id: '',
                    level: '',
                    grade: '',
                    parent_position_id: ''
                });
                // Refresh positions data
                getCustomerUsersInfo();
                
                // Refresh positions list
                const positionsResponse = await get('/api/tenant/positions');
                if (positionsResponse && positionsResponse.data) {
                    setPositions(positionsResponse.data);
                }
            }
        } catch (error) {
            console.error('Error creating position:', error);
            toast({
                title: "Error",
                description: "Failed to create position",
                variant: "destructive",
            });
        }
    };

    const handleInputChange = (field, value) => {
        setNewPosition(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Function to find position title by ID
    const getPositionTitle = (id) => {
        const title = positionTitles.find(title => title._id === id);
        return title ? title.position_title : 'N/A';
    };

    // Function to find position by ID
    const getPosition = (id) => {
        const position = positions.find(pos => pos._id === id);
        return position ? position : null;
    };

    return (
        <div className="border rounded-md p-4">
            <div className="bg-white rounded-lg ">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium">Positions</h2>
                    <Button onClick={() => setIsAddModalOpen(true)}>
                        Add New Position
                    </Button>
                </div>
                
                {/* Add Position Dialog */}
                <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Add New Position</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="position_title" className="text-right">
                                    Position Title
                                </Label>
                                <div className="col-span-3">
                                    <SearchableSelect
                                        options={positionTitles.map(title => ({ 
                                            value: title._id, 
                                            label: title.position_title 
                                        }))}
                                        value={newPosition.position_title_id}
                                        onValueChange={(value) => handleInputChange('position_title_id', value)}
                                        placeholder="Select a position title"
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="level" className="text-right">
                                    Level
                                </Label>
                                <Input
                                    id="level"
                                    type="number"
                                    className="col-span-3"
                                    value={newPosition.level}
                                    onChange={(e) => handleInputChange('level', e.target.value)}
                                />
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="grade" className="text-right">
                                    Grade
                                </Label>
                                <Input
                                    id="grade"
                                    type="number"
                                    className="col-span-3"
                                    value={newPosition.grade}
                                    onChange={(e) => handleInputChange('grade', e.target.value)}
                                />
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="parent_position" className="text-right">
                                    Parent Position
                                </Label>
                                <div className="col-span-3">
                                    <SearchableSelect
                                        options={positions.map(position => ({ 
                                            value: position._id, 
                                            label: `${getPositionTitle(position.position_title_id)} - Level ${position.level}` 
                                        }))}
                                        value={newPosition.parent_position_id}
                                        onValueChange={(value) => handleInputChange('parent_position_id', value)}
                                        placeholder="Select a parent position"
                                    />
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                                Cancel
                            </Button>
                            <Button 
                                onClick={handleAddPosition}
                                disabled={!newPosition.position_title_id || !newPosition.level || !newPosition.grade}
                            >
                                Create Position
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
                
                {/* Positions Table */}
                <div className="mt-6">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Position Title</TableHead>
                                <TableHead>Level</TableHead>
                                <TableHead>Grade</TableHead>
                                <TableHead>Parent Position</TableHead>
                                <TableHead>Roles</TableHead>
                                <TableHead>Cell/Box</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {positions.map((position) => (
                                <TableRow key={position._id}>
                                    <TableCell className="font-medium" title={position._id}>
                                        {(position.position_title || (position.position_title_txt ? 'XX ' + position.position_title_txt : 'N/A'))}
                                        <span title={position.description}>&nbsp;&nbsp;&nbsp;.</span>
                                    </TableCell>
                                    <TableCell>{position.level}</TableCell>
                                    <TableCell>{position.grade}</TableCell>
                                    <TableCell title={position.parent_position_id}>
                                        {position.parent_position_title ? 
                                            ((position.parent_position_title)) || 'N/A' : 
                                            'None'}
                                    </TableCell>
                                    <TableCell title={Array.isArray(position.role_ids) &&position.role_ids.join(',')}>{Array.isArray(position.role_ids) && position.role_ids.length > 0 ? position.role_ids.length : 'None'   }</TableCell> 
                                    <TableCell title={position.department_id}>
                                        {position.department_id ? 
                                            ((position.department_id)) || 'N/A' : 
                                            'None'}
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            onClick={() => {
                                                setSelectedPosition(position);
                                                setIsEditModalOpen(true);
                                            }}
                                        >
                                            <PencilIcon className="h-4 w-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
                
                {/* Edit Position Dialog */}
                <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Edit Position</DialogTitle>
                        </DialogHeader>
                        {selectedPosition && (
                            <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_position_title" className="text-right">
                                        Position Title
                                    </Label>
                                    <div className="col-span-3">
                                        <SearchableSelect
                                            options={positionTitles.map(title => ({ 
                                                value: title._id, 
                                                label: title.position_title 
                                            }))}
                                            value={selectedPosition.position_title_id}
                                            onValueChange={(value) => setSelectedPosition({...selectedPosition, position_title_id: value})}
                                            placeholder="Select a position title"
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_level" className="text-right">
                                        Level
                                    </Label>
                                    <Input
                                        id="edit_level"
                                        type="number"
                                        className="col-span-3"
                                        value={selectedPosition.level}
                                        onChange={(e) => setSelectedPosition({...selectedPosition, level: e.target.value})}
                                    />
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_grade" className="text-right">
                                        Grade
                                    </Label>
                                    <Input
                                        id="edit_grade"
                                        type="number"
                                        className="col-span-3"
                                        value={selectedPosition.grade}
                                        onChange={(e) => setSelectedPosition({...selectedPosition, grade: e.target.value})}
                                    />
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit_parent_position" className="text-right">
                                        Parent Position
                                    </Label>
                                    <div className="col-span-3">
                                        <SearchableSelect
                                            options={[{ value: '', label: 'None' }, ...positions.map(position => ({ 
                                                value: position._id, 
                                                label: `${getPositionTitle(position.position_title_id)} - Level ${position.level}` 
                                            }))]}
                                            value={selectedPosition.parent_position_id || ''}
                                            onValueChange={(value) => setSelectedPosition({...selectedPosition, parent_position_id: value})}
                                            placeholder="Select a parent position"
                                        />
                                    </div>
                                </div>
                            </div>
                        )}
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={async () => {
                                try {
                                    // Remove empty parent_position_id
                                    const updateData = { ...selectedPosition };
                                    if (updateData.parent_position_id === '') {
                                        delete updateData.parent_position_id;
                                    }
                                    
                                    const response = await put(`/api/tenant/positions/${selectedPosition._id}`, updateData);
                                    if (response && response.data) {
                                        toast({
                                            title: "Success",
                                            description: "Position updated successfully",
                                        });
                                        setIsEditModalOpen(false);
                                        setSelectedPosition(null);
                                        // Refresh positions data
                                        getCustomerUsersInfo();
                                        
                                        // Refresh positions list
                                        const positionsResponse = await get('/api/tenant/positions');
                                        if (positionsResponse && positionsResponse.data) {
                                            setPositions(positionsResponse.data);
                                        }
                                    }
                                } catch (error) {
                                    console.error('Error updating position:', error);
                                    toast({
                                        title: "Error",
                                        description: "Failed to update position",
                                        variant: "destructive",
                                    });
                                }
                            }}>
                                Update Position
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </div>
    );
};

export default PositionzCard;
