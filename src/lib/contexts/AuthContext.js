import { createContext, useContext, useEffect, useState } from 'react';
import { useSession, signIn, signOut } from "next-auth/react";
import { useRouter } from 'next/router';
import { jwtDecode } from "jwt-decode";

const AuthContext = createContext();

// Configurable buffer time
const TOKEN_REFRESH_BUFFER = 15 * 1000; // 30 seconds in milliseconds

export function AuthProvider({ children }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentToken, setCurrentToken] = useState(null);

  const refreshToken = async () => {
    if (!session?.user?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: session.user.refreshToken }),
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const { token: newToken } = await response.json();
      setCurrentToken(newToken);
      return newToken;
    } catch (error) {
      signOut({ callbackUrl: "/auth/signin" });
      throw error;
    }
  };

  const getToken = async () => {
    if (currentToken) {
      try {
        const decodedToken = jwtDecode(currentToken);
        const tokenExp = new Date(decodedToken.exp * 1000);

        if (tokenExp.getTime() - Date.now() > TOKEN_REFRESH_BUFFER) {
          // console.log('expires', tokenExp, tokenExp.getTime());
          console.log('kalan', tokenExp.getTime() - Date.now(), TOKEN_REFRESH_BUFFER);
          return currentToken;
        }
      } catch (error) {
        console.error('Current token decode failed:', error);
        // Current token bozuksa null yap
        setCurrentToken(null);
      }
    }

    if (!session?.user?.token) {
      router.push('/auth/signin');
      return null;
    }

    // Session token'ını kontrol et
    try {
      const sessionTokenDecoded = jwtDecode(session.user.token);
      const sessionTokenExp = new Date(sessionTokenDecoded.exp * 1000);

      if (sessionTokenExp.getTime() - Date.now() > TOKEN_REFRESH_BUFFER) {
        console.log('Using session token');
        setCurrentToken(session.user.token);
        return session.user.token;
      }
    } catch (error) {
      console.error('Session token decode failed:', error);
      // Session token da bozuksa refresh dene
    }

    try {
      const newToken = await refreshToken();
      return newToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return null;
    }
  };

  const login = async (email, password) => {
    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });
      if (result.error) throw new Error('Invalid email or password'); //result.error
      return result;
    } catch (error) {
      throw error;
    }
  };

  const loginWithGoogle = async () => {
    try {
      await signIn("google", { callbackUrl: "/app/dashboard" });
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    signOut({ callbackUrl: "/" });
  };

  const register = async (userData) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Registration failed');
      }

      const data = await response.json();
      
      // Auto login after successful registration
      if (!data.isInvited) {
        const loginResult = await signIn("credentials", {
          email: userData.email,
          password: userData.password,
          redirect: false,
        });

        if (loginResult.error) {
          throw new Error('Auto login failed');
        }
      }

      return { ...data, success: true };
    } catch (error) {
      throw error;
    }
  };

  const forgotPassword = async (email) => {
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to process password reset request');
      }

      return response.json();
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (token, password) => {
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to reset password');
      }

      return response.json();
    } catch (error) {
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user: session?.user,
      status,
      isAuthenticated: !!session,
      token: currentToken || session?.user?.token,
      getToken,
      login,
      logout,
      loginWithGoogle,
      register,
      forgotPassword,
      resetPassword
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
