import { useAuth } from '../contexts/AuthContext';

export const useApiClient = () => {
  const { getToken } = useAuth();

  const fetchWithAuth = async (url, options = {}, fnxOptions = {}) => {
    try {
      const token = await getToken();

      // Debug: Token'ı kontrol et
      if (!token) {
        console.error('No token available for request:', url);
        return false;
      }

      console.log('Making request to:', url, 'with token length:', token?.length);

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      };

      fnxOptions?.debug && console.log('fetchWithAuth', url, options);
      
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        // throw new Error(data.message || `HTTP error! status: ${response.status}`);
        console.log('error:', data.message || `HTTP error! status: ${response.status}`);
        return false;
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      // throw error;
      return false;
    }
  };

  return {
    get: (url, data, fnxOptions) => fetchWithAuth(url, { method: 'GET' }, fnxOptions),
    post: (url, data, fnxOptions) => fetchWithAuth(url, {
      method: 'POST',
      body: JSON.stringify(data),
    }, fnxOptions),
    put: (url, data, fnxOptions) => fetchWithAuth(url, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, fnxOptions),
    delete: (url, fnxOptions) => fetchWithAuth(url, { method: 'DELETE' }, fnxOptions),
  };
};
