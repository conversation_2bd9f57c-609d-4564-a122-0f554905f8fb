import bcrypt from 'bcrypt';
import clientPromise from '@/lib/db/mongodb';

import {vars} from '@/lib/constants';

const usersCollection = vars.db.collection.users;
const customersCollection = vars.db.collection.customers;
const invitationsCollection = vars.db.collection.invitations;

const dbSchema = 'tflouu_L0';
const dbColls = schema => {
  return {
    position_titles: `${schema}.app.auth.position_titles`,
    positions: `${schema}.app.auth.positions`,
    position_roles: `${schema}.app.auth.position_roles`,
    roles: `${schema}.app.auth.roles`,
    permissions: `${schema}.app.auth.permissions`,
  };
}
const dbName = vars.db.dbName;
export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

export async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

export async function findUserByEmail(email, tenantId) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  let q = {
    "email": email,
    $or: [
      { "status": { $exists: true, $in: ["active", null, ""] } },
      { "status": { $exists: false } }
    ]
  }
  if (tenantId) {
    q["tenants.clientId"] = tenantId;
  }
  return await db.collection(usersCollection).findOne(q);
}


export async function findPositionsAndRoles({
    externalPositionIds, 
    schemaDB = dbSchema,
    schemaCollections = 'vmrwaswszr',
    tenantId
  }) {
  const client = await clientPromise;
  const db = client.db(schemaDB);
  let q = [
    // 1. Dışarıdan gelen positionId'lere göre filtrele
    {
      $match: {
        _id: { $in: externalPositionIds }
      }
    },

    // 2. position_titles ile birleştir: position_title_id üzerinden
    {
      $lookup: {
        from: dbColls(schemaCollections).position_titles,
        localField: "position_title_id",
        foreignField: "_id",  // position_titles._id
        as: "position_title_info"
      }
    },
    {
      $unwind: {
        path: "$position_title_info",
        preserveNullAndEmptyArrays: true
      }
    },

    // 3. position_roles ile birleştir: position_id üzerinden
    // {
    //   $lookup: {
    //     from: dbColls(schemaCollections).position_roles,
    //     localField: "_id",
    //     foreignField: "position_id",
    //     as: "assigned_roles"
    //   }
    // },

    // 4. role_ids ile roles koleksiyonuna eriş
    {
      $lookup: {
        from: dbColls(schemaCollections).roles,
        localField: "role_ids",
        foreignField: "_id",
        as: "role_details"
      }
    },

    // 5. rollerin permissions alanını permissions koleksiyonu ile genişlet
    {
      $lookup: {
        from: dbColls(schemaCollections).permissions,
        localField: "role_details.permissions",
        foreignField: "_id",
        as: "all_permissions"
      }
    },

    // 6. Sonuçları proje et
    {
      $project: {
        _id: 1,
        position_title: "$position_title_info.position_title",
        level: 1,
        grade: 1,
        status: 1,
        parent_position_id: 1,
        roles: {
          $map: {
            input: "$role_details",
            as: "role",
            in: {
              role_name: "$$role.roleName",
              role_description: "$$role.description",
              permissions: {
                $map: {
                  input: {
                    $filter: {
                      input: "$all_permissions",
                      cond: { $in: ["$$this._id", "$$role.permissions"] }
                    }
                  },
                  as: "perm",
                  in: {
                    permission_code: "$$perm.code",
                    module: "$$perm.module",
                    permission: "$$perm.permission",
                    access: "$$perm.access"
                  }
                }
              }
            }
          }
        }
      }
    },

    // 7. Opsiyonel: Sadece aktif pozisyonlar
    {
      $match: {
        status: "active"
      }
    }
  ];
  // console.log('findPositionsAndRoles - aggregation query:', JSON.stringify(q, null, 2));
  return await db.collection(dbColls(schemaCollections).positions).aggregate(q).toArray();
}


export async function findTenantData(tenantId) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  let q = {
    "clientId": tenantId,
  }
  return await db.collection(customersCollection).findOne(q);
}


export async function createUser(userData) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const user = {
    ...userData,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: userData.role || 'member',
    isActive: true,
    onboardingCompleted: true, //TODO: Implement onboarding process
  };
  
  const result = await db.collection(usersCollection).insertOne(user);
  return { ...user, _id: result.insertedId };
}

export async function checkInvitation(email, clientId) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  const q = {
    email,
    used: false,
    $or: [
      { status: { $exists: false } },
      { status: 'pending' }
    ],
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
    // status: 'pending',
    // expiresAt: { $gt: new Date() }
  };
  const invitation = await db.collection(invitationsCollection).findOne(q);
  console.log('invitation check:', invitationsCollection, JSON.stringify(q), invitation);
  return invitation;
}

export async function markInvitationUsed(email) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  await db.collection(invitationsCollection).updateOne(
    { email, status: 'pending' },
    { 
      $set: { 
        status: 'used',
        usedAt: new Date()
      }
    }
  );
}

export async function updateUserRole(userId, role) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  await db.collection(usersCollection).updateOne(
    { _id: userId },
    { 
      $set: { 
        role,
        updatedAt: new Date()
      }
    }
  );
}

export async function getModulesWithAccessPermission(user) {
  // Sonuçları tutmak için Set (tekrar etmemesi için)
  const modules = new Set();

  // tenantData.positionsAndRoles dizisini kontrol et
  if (user.tenantData?.positionsAndRoles && Array.isArray(user.tenantData.positionsAndRoles)) {
    user.tenantData.positionsAndRoles.forEach(pos => {
      if (pos.roles && Array.isArray(pos.roles)) {
        pos.roles.forEach(role => {
          if (role.permissions && Array.isArray(role.permissions)) {
            role.permissions.forEach(perm => {
              if (perm.permission === "access") {
                modules.add(perm.module);
              }
            });
          }
        });
      }
    });
  }

  // Set'i diziye çevir ve döndür
  return Array.from(modules);
}

export async function getTenantData({user, clientId}) {
  const tenantInfo = user.tenants ? user.tenants.find(t => t.clientId === clientId) : null;
  if (clientId && !tenantInfo) {
    console.log({ message: 'User is not part of the specified tenant', clientId, userTenants: user.tenants });
    return false
  }
  const tenantData = tenantInfo ? await findTenantData(user?.tenants[0]?.clientId) : null;
  if (tenantData?.isActive === false) {
    console.log({ message: 'Tenant is not active', tenantData });
    return false;
  }
  // console.log('getTenantData tenantData?.adminUserID:', tenantData?.adminUserID.toString());
  // console.log('UserID:', user._id.toString());
  let resp = {
      id: tenantData?._id.toString() || '',
      clientId: tenantData?.clientId || '',
      name: tenantData?.customerName || '',
      subsPlanID: tenantData?.subsPlanID || '',
      subsContractID: tenantData?.subsContractID || '',
      isActive: tenantData?.isActive || false,
      isTenantAdmin: tenantData?.adminUserID.toString() === user._id.toString(),
      modules: tenantData?.modules || [],
      positions: user?.tenants[0]?.positions || [],
  };

  let externalPositionIds = user.tenants[0]?.positions || [];
  const positionsAndRoles = await findPositionsAndRoles({
    externalPositionIds,
    schemaDB: tenantData?.dbSchema,
    schemaCollections: tenantData?.dbCollections,
    tenantId: clientId
  });

  resp.positionsAndRoles = positionsAndRoles || [];
  resp.accessibleModules = await getModulesWithAccessPermission(resp);
  return resp;
}