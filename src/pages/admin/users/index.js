import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

import Layout from '@/components/layouts/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import DepartmentzCard from '@/components/adminpanel/users_departmant_card.js';
import AccountUsersInfoCard from '@/components/adminpanel/users_info_card.js';
import PositionzCard from '@/components/adminpanel/users_position_card.js';

import PositionTitlesCard from '@/components/adminpanel/users_position_titles_card.js';

export default function AdminDashboard() {
    const router = useRouter();
    const { data: session, status } = useSession();
    const [analytics, setAnalytics] = useState(null);
    const [loading, setLoading] = useState(true);
    //get customer info from api/tenant..
    const [customerInfo, setCustomerInfo] = useState(null);
    const [customerUsersInfo, setCustomerUsersInfo] = useState(null);

    const [customerPositions, setCustomerPositions] = useState(null);
    const [departmentz, setDepartmentz] = useState(null);
    const [positionz, setPositionz] = useState(null);
    const [positionTitles, setPositionTitles] = useState(null);
    useEffect(() => {
        if (status === 'unauthenticated') {
            // User is not authenticated, redirect to signin
            router.push('/auth/signin');
            return;
        }
        setLoading(false);
    }, [session]);

    useEffect(() => {
        status !== 'loading' && session && console.log('Session updated in Dashboard with value:', session);
    }, [session, status]);

    const getCustomerInfo = async () => {
        try {
            const res = await fetch('/api/tenant/info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                setCustomerInfo(data);
            }
        } catch (error) {
            console.error('Error fetching customer info:', error);
        }
    };

    const getCustomerUsersInfo = async () => {
        try {
            const res = await fetch('/api/tenant/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                // console.log('Customer Users Info:', data);
                setCustomerUsersInfo(data);
            }
        } catch (error) {
            console.error('Error fetching customer users info:', error);
        }
    };

    const getDepartmentz = async () => {
        try {
            const res = await fetch('/api/tenant/departmentz', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                console.log('Departmentz:', data);
                setDepartmentz(data.data || []);
            }
        } catch (error) {
            console.error('Error fetching customer info:', error);
        }
    };

    const getPositionz = async () => {
        try {
            const res = await fetch('/api/tenant/positions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                console.log('positions:', data);
                setPositionz(data.data || []);
            }
        } catch (error) {
            console.error('Error fetching positions info:', error);
        }
    };

    const getPositionTitles = async () => {
        try {
            const res = await fetch('/api/tenant/position_titles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                console.log('positions Titles:', data);
                setPositionTitles(data.data || []);
            }
        } catch (error) {
            console.error('Error fetching positions info:', error);
        }
    };
    useEffect(() => {
        if (session?.token) {
            getCustomerInfo();
            getCustomerUsersInfo();
            getDepartmentz();
            getPositionz();
            getPositionTitles();
        }
    }, [session?.token]);

    if (loading) {
        return (
            <ProtectedRoute>
                <Layout title="Dashboard">
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                </Layout>
            </ProtectedRoute>
        );
    }
    return (
        <ProtectedRoute>
            <Layout title="Dashboard">
                <div className="space-y-6">
                    {/* Welcome Section */}
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
                        <h1 className="text-2xl font-bold mb-2">
                            {session?.user?.tenantData?.name} App Users !
                        </h1>
                        <p className="text-blue-100">
                            Here's what's happening with your account today.
                        </p>
                    </div>
                    {/* Responsive Grid Layout */}
                    <div className="grid grid-cols-1 md:grid-cols-10 gap-6">
                        <div className="md:col-span-10">
                            {/* tabs Actions */}
                            <Tabs defaultValue="users" className="w-full">
                                <TabsList className="grid w-full grid-cols-5">
                                    <TabsTrigger value="users">Users</TabsTrigger>
                                    <TabsTrigger value="positions">Positions</TabsTrigger>
                                    <TabsTrigger value="roles">Roles</TabsTrigger>
                                    <TabsTrigger value="departments">Departments</TabsTrigger>
                                    <TabsTrigger value="permissions">Permissions</TabsTrigger>
                                </TabsList>
                                <TabsContent value="users">
                                    <AccountUsersInfoCard 
                                        customerUsersInfo={customerUsersInfo}
                                        customerPositions={customerPositions}
                                        session={session}
                                        getCustomerUsersInfo={getCustomerUsersInfo}
                                    />
                                </TabsContent>
                                <TabsContent value="positions">
                                    <div className="grid grid-cols-1 md:grid-cols-10 gap-6">
                                        <div className="md:col-span-7">
                                            {/* Main content for positions */}
                                            <PositionzCard 
                                                positionz={positionz}
                                                session={session}
                                                getCustomerUsersInfo={getPositionz}
                                            />
                                        </div>
                                        <div className="md:col-span-3">
                                            {/* Secondary content (30% width on desktop, bottom on mobile) */}
                                            <PositionTitlesCard 
                                                session={session}
                                                positionTitles={positionTitles}
                                                getCustomerUsersInfo={getPositionTitles}
                                            />
                                        </div>
                                    </div>
                                </TabsContent>
                                <TabsContent value="roles">
                                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                                        <h2 className="text-lg font-medium">Roles</h2>
                                        <p className="text-gray-500">Role management content will go here.</p>
                                    </div>
                                </TabsContent>
                                <TabsContent value="departments">
                                    <DepartmentzCard 
                                        departmentz={departmentz}
                                        session={session}
                                        getCustomerUsersInfo={getDepartmentz}
                                    />
                                </TabsContent>
                                <TabsContent value="permissions">
                                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                                        <h2 className="text-lg font-medium">Permissions</h2>
                                        <p className="text-gray-500">Permission management content will go here.</p>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </div>
                    </div>
                </div>
            </Layout>
        </ProtectedRoute>
    );
}
