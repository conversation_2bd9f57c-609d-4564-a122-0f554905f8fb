// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant User Info API called', user);
    const { method } = req;
    
    switch (method) {
        case 'POST':
            try {
                let customerPositionsData = [];
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    customerPositionsData = await getPositionsData(user.tenantData.clientId, customerData);
                }
                // console.log('customerUsersData:', customerUsersData.length, JSON.stringify(customerUsersData, null, 2), customerData);
                res.status(200).json({ data: customerPositionsData });
            } catch (error) {
                console.error('Tenant User Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
        case 'PUT':
            try {
                const { id } = req.query;
                const updateData = req.body;
                
                if (!id) {
                    return res.status(400).json({ message: 'Position ID is required' });
                }
                
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }
                
                const result = await updatePositionData(customerData, id, updateData);
                
                if (result) {
                    res.status(200).json({ data: result, message: 'Position updated successfully' });
                } else {
                    res.status(404).json({ message: 'Position not found' });
                }
            } catch (error) {
                console.error('Error updating position:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
        default:
            res.setHeader('Allow', ['POST', 'PUT']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}

const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}

const getPositionsData = async (clientId, customerData, fields) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);
    const q = [
        {
            $match: {
                "status": "active"
            }
        },
        {
            $lookup: {
                from: clientSchema + vars.db.actCollections.position, // Aynı koleksiyon içinde self-join
                localField: "parent_position_id",
                foreignField: "_id",
                as: "parent_position"
            }
        },
        { "$addFields": { "parent_position": { "$first": "$parent_position" } } },
        {
            $lookup: {
                from: clientSchema + vars.db.actCollections.position_titles, // ".app.auth.position_titles",
                localField: "position_title_id",
                foreignField: "_id",
                as: "position_title"
            }
        },
        { "$addFields": { "position_title": { "$first": "$position_title" } } },
        { "$addFields": { "position_title": "$position_title.position_title" } },

        {
            $lookup: {
                from: clientSchema + vars.db.actCollections.position_titles, // ".app.auth.position_titles",
                localField: "parent_position.position_title_id",
                foreignField: "_id",
                as: "parent_position_title"
            }
        },

        { "$addFields": { "parent_position_title": { "$first": "$parent_position_title" } } },
        { "$addFields": { "parent_position_title": "$parent_position_title.position_title" } },
        {
            "$project": fields || {
                description: 1,
                level: 1,
                grade: 1,
                department_id: 1,
                position_title_id: 1,
                position_title_txt: 1,
                position_title: 1,
                parent_position_id: 1,
                parent_position_title: 1,
                role_ids: 1, createdAt: 1, updatedAt: 1, status: 1, isManagementRole: 1
            },
        }
    ];
    const qx = [
        {
            $match: {
                "status": "active"
            }
        },
        {
            $lookup: {
                from: clientSchema + vars.db.actCollections.position_titles, // ".app.auth.position_titles",
                localField: "position_title_id",
                foreignField: "_id",
                as: "title_info"
            }
        },
        {
            $unwind: {
                path: "$title_info",
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $project: {
                ...fields,
            }
        }
    ];

    const positions = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!positions) {
        return null;
    }
    return positions;
}


const getCustomerUsersData = async (clientId, customerData) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const q = [
        // 1. tenants dizisinde istenen clientId'e sahip olanları bul
        {
            $match: {
                "tenants.clientId": clientId
            }
        },

        // 2. tenants dizisini aç
        {
            $unwind: "$tenants"
        },

        // 3. Sadece istenen clientId'e sahip tenant'ı filtrele
        {
            $match: {
                "tenants.clientId": clientId
            }
        },

        // 4. Gereksiz alanları temizle ve çıktı yapısını düzenle
        {
            $project: {
                _id: 1,
                email: 1,
                name: 1,
                status: 1,
                provider: 1,
                createdAt: 1,
                updatedAt: 1,
                isActive: 1,
                onboardingCompleted: 1,
                preferences: 1,
                profile: 1,
                tenantData: {
                    clientId: "$tenants.clientId",
                    clientSchema: "$tenants.clientSchema",
                    customerName: "$tenants.customerName",
                    roleId: "$tenants.roleId",
                    roleCode: "$tenants.roleCode",
                    positions: "$tenants.positions"
                }
            }
        }
    ]
    const users = await db.collection(vars.db.collection.users).aggregate(q).toArray();
    if (!users) {
        return null;
    }
    // console.log('users found:', users.length);
    users.forEach(user => {
        user.id = user._id.toString();
        delete user._id;
    });

    //tum user objelerinin altındaki tenantData dizindeki position_id leri bir dizin haline getir
    let positionIds = [];
    users.forEach(user => {
        if (user.tenantData?.positions && Array.isArray(user.tenantData.positions)) {
            user.tenantData.positions.forEach(pos => {
                if (pos) {
                    positionIds.push(pos);
                }
            });
        }
    });
    //benzersiz yap
    positionIds = [...new Set(positionIds.map(id => id.toString()))] //;.map(id => new ObjectId(id));
    // console.log('positionIds to find:', positionIds.length, positionIds);
    if (positionIds.length !== 0) {
        const positions = await getPositionsById(customerData, positionIds)
        if (!positions) {
            return null;
        }
        // console.log('positions found:', positions.length, positions, positionIds);
        
        // Position verilerini user objelerine ekle
        const positionsMap = positions.reduce((acc, position) => {
            acc[position._id.toString()] = position;
            return acc;
        }, {});
        
        users.forEach(user => {
            if (user.tenantData?.positions && Array.isArray(user.tenantData.positions)) {
                user.tenantData.positions = user.tenantData.positions.map(posId => {
                    const positionData = positionsMap[posId.toString()];
                    return {
                        id: posId,
                        ...positionData
                    };
                });
            }
        });
    }
    
        // console.log('positions found:', positions.length, positions, positionIds);
        console.log('users found:', users.length, users);
    return users;
}

async function getPositionsById(customerData, positionsIdArray, fields = {
    _id: 1,
    position_title: "$title_info.position_title",
    // description: 1,
    // level: 1,
    // grade: 1,
    status: 1,
    parent_position_id: 1,
    // role_ids: 1,
    // createdAt: 1,
    // updatedAt: 1
}
) {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);
    // let positionsIdArrayObj = positionsIdArray.map(id => ObjectId.createFromHexString(id));
    let positionsIdArrayObj = positionsIdArray.map(id => new ObjectId(id));
    // console.log('dbTitleCollName:', dbTitleCollName, positionsIdArrayObj);
    const q = [
        {
            $match: {
                "_id": { $in: positionsIdArrayObj }
            }
        },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles",
                localField: "position_title_id",
                foreignField: "_id",
                as: "title_info"
            }
        },
        {
            $unwind: {
                path: "$title_info",
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $project: {
                ...fields,
            }
        }
    ];
    let resp = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsById - aggregation result:', JSON.stringify(resp, null, 2));
    return resp;
    // return await dbClient.collection(dbTitleCollName).aggregate().toArray();
}

const updatePositionData = async (customerData, id, updateData) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);
    
    // Remove fields that shouldn't be updated
    const { _id, createdAt, updatedAt, ...cleanUpdateData } = updateData;
    
    // Add updatedAt timestamp
    cleanUpdateData.updatedAt = new Date();
    
    try {
        const result = await dbClient.collection(dbTitleCollName).updateOne(
            { _id: new ObjectId(id) },
            { $set: cleanUpdateData }
        );
        
        if (result.matchedCount === 0) {
            return null;
        }
        
        // Return the updated position
        const updatedPosition = await dbClient.collection(dbTitleCollName).findOne(
            { _id: new ObjectId(id) }
        );
        
        return updatedPosition;
    } catch (error) {
        console.error('Error updating position:', error);
        throw error;
    }
};
