// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant User Info API called', user);
    const { method } = req;
    
    switch (method) {
        case 'POST':
            try {
                let departments = [];
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    departments = await getDepartmentsData(user.tenantData.clientId, customerData);
                }
                // console.log('customerUsersData:', customerUsersData.length, JSON.stringify(customerUsersData, null, 2), customerData);
                res.status(200).json({ data: departments });
            } catch (error) {
                console.error('Tenant Departments Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        case 'PUT':
            try {
                const { name, type, parentId, createdBy } = req.body;
                
                if (!name || !type) {
                    return res.status(400).json({ message: 'Name and type are required' });
                }
                
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }
                
                // Create new department object with required fields
                const newDepartment = {
                    name,
                    type,
                    parentId: parentId || null,
                    dtCreated: new Date(),
                    dtUpdated: new Date(),
                    createdBy: createdBy || user?.id,
                    updatedBy: createdBy || user?.id,
                    status: 'active'
                };
                
                // Save to database
                const result = await saveDepartmentData(customerData, newDepartment);
                
                if (result) {
                    res.status(200).json({ message: 'Department created successfully', data: result });
                } else {
                    res.status(500).json({ message: 'Failed to create department' });
                }
            } catch (error) {
                console.error('Tenant Departments Create API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        case 'DELETE':
            try {
                const { departmentId } = req.body;
                
                if (!departmentId) {
                    return res.status(400).json({ message: 'Department ID is required' });
                }
                
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }
                
                // Update department status to 'deleted'
                const result = await updateDepartmentStatus(customerData, departmentId, 'deleted');
                
                if (result) {
                    res.status(200).json({ message: 'Department deleted successfully' });
                } else {
                    res.status(500).json({ message: 'Failed to delete department' });
                }
            } catch (error) {
                console.error('Tenant Departments Delete API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        case 'PATCH':
            try {
                const { departmentId, name, type, parentId } = req.body;
                
                if (!departmentId || !name || !type) {
                    return res.status(400).json({ message: 'Department ID, name, and type are required' });
                }
                
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }
                
                // Update department
                const result = await updateDepartmentData(customerData, departmentId, {
                    name,
                    type,
                    parentId: parentId || null,
                    dtUpdated: new Date(),
                    updatedBy: user?.id || 'system'
                });
                
                if (result) {
                    res.status(200).json({ message: 'Department updated successfully', data: result });
                } else {
                    res.status(500).json({ message: 'Failed to update department' });
                }
            } catch (error) {
                console.error('Tenant Departments Update API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        default:
            res.setHeader('Allow', ['POST', 'PUT', 'DELETE', 'PATCH']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}

const updateDepartmentData = async (customerData, departmentId, updateData) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.departments_lines}`;
    const dbClient = client.db(clientDB);
    
    try {
        const result = await dbClient.collection(dbTitleCollName).updateOne(
            { _id: new ObjectId(departmentId) },
            { $set: updateData }
        );
        return result;
    } catch (error) {
        console.error('Error updating department data:', error);
        return null;
    }
}

const updateDepartmentStatus = async (customerData, departmentId, status) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.departments_lines}`;
    const dbClient = client.db(clientDB);
    
    try {
        const result = await dbClient.collection(dbTitleCollName).updateOne(
            { _id: new ObjectId(departmentId) },
            { 
                $set: { 
                    status: status,
                    dtUpdated: new Date(),
                    updatedBy: customerData?.updatedBy || 'system'
                }
            }
        );
        return result;
    } catch (error) {
        console.error('Error updating department status:', error);
        return null;
    }
}

const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}
const getDepartmentsData = async (clientId, customerData, fields = {
    //  _id: 0,
    createdAt: 0,
    // updatedAt: 1
}) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.departments_lines}`;
    const dbClient = client.db(clientDB);
    const q = [
        {
            $match: {
                $or: [
                    { "status": { $exists: false } },
                    { "status": null },
                    { "status": "" },
                    { "status": "active" }
                ]
            }
        },
        {
            $project: {
                ...fields,
            }
        }
    ];

    const positions = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!positions) {
        return null;
    }
    return positions;
}

const saveDepartmentData = async (customerData, departmentData) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.departments_lines}`;
    const dbClient = client.db(clientDB);
    
    try {
        const result = await dbClient.collection(dbTitleCollName).insertOne(departmentData);
        return result;
    } catch (error) {
        console.error('Error saving department data:', error);
        return null;
    }
}
